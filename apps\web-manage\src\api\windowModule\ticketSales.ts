import { requestClient } from '#/api/request';

// 获取票种分类
export const ticketTypeList = (params: any) => {
  return requestClient.get('/win/ticketType/list', { params });
};

// 获取门票列表
export const ticketList = (params: any) => {
  return requestClient.get('/win/ticket/list', { params });
};

// 获取门票详情
export const ticketInfo = (params: any) => {
  return requestClient.get('/win/ticket/info', { params });
};

// 获取门票分时预约配置
export const periodInfo = (params: any) => {
  return requestClient.get('/tkt/ticket/periodInfo', { params });
};

// 售票下单
export const ticketCreateOrder = (params: any) => {
  return requestClient.post('/win/ticket/createOrder', params);
};
