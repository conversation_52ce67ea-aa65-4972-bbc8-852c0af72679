<script lang="ts" setup>
import { useEventBus } from '#/utils/eventBus';
import { ref, provide } from 'vue';
import {
  InputSearch,
  Space,
  DatePicker,
  Tabs,
  TabPane,
  Empty,
  Button,
  message,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { confirm } from '@vben/common-ui';

import { ticketTypeList, ticketList } from '#/api/windowModule/ticketSales';

// =======================================================================================
const disabledDate = (current: Dayjs) => {
  return (
    (current && current < dayjs().startOf('day')) ||
    current > dayjs().add(1, 'year').startOf('day')
  );
};
let currentDate: any = ref(dayjs());
const searchTime = () => {
  if (selectAll.value && selectAll.value.length) {
    confirm('切换时间后，已选门票将会清空，是否确认继续？')
      .then(() => {
        selectAll.value = [];
        selectAll2.value = [];
        getList();
      })
      .catch(() => {
        currentDate.value = dayjs(getListParams.value.playDate) || dayjs();
      });
  } else {
    getList();
  }
};

// ========================================================================================
let typeList: any = ref([]);
const getTypeList = async () => {
  let resData = await ticketTypeList({});
  typeList.value = resData;
  let params = { id: 0, typeName: '全部' };
  typeList.value.unshift(params);
  getListParams.value.typeId = params.id;
};
const typeBtn = (e: any) => {
  getListParams.value.typeId = e;
  getList();
};

// ========================================================================================
let getListParams = ref({
  name: '',
  playDate: '',
  typeId: 0,
});
let listData: any = ref([]);
const getList = async () => {
  getListParams.value.playDate = currentDate.value.format('YYYY-MM-DD');
  listData.value = [];
  let params: any = { ...getListParams.value };
  let resData = await ticketList(params);
  listData.value = resData;
};

// ========================================================================================
import listItemTem from './components/listItem.vue';
let selectAll: any = ref([]);
let selectAll2: any = ref([]);
const listSelect = (val: any) => {
  let exists = selectAll.value.some(
    (item: any) => JSON.stringify(item) === JSON.stringify(val),
  );
  if (exists) {
    let index = selectAll.value.findIndex((e: any) => {
      return JSON.stringify(e) === JSON.stringify(val);
    });

    if (
      !val.isOrderQuantity ||
      selectAll2.value[index].ticketNum < val.orderQuantity
    ) {
      selectAll2.value[index].ticketNum++;
    } else {
      message.warning(`限购${val.orderQuantity}张`);
    }
  } else {
    selectAll.value.unshift(val);
    selectAll2.value.unshift({ ...val, ticketNum: 1 });
  }
};

// ====
import buyInfoTem from './buyInfo/index.vue';
const delSelect = (val: any) => {
  let index = selectAll2.value.findIndex((e: any) => {
    return e.ticketId === val.ticketId;
  });
  selectAll.value.splice(index, 1);
  selectAll2.value.splice(index, 1);
};
provide('ticketSalesDelSelect', delSelect);

// =================================================================================
useEventBus('ticketSalesListRefreshData', (e: any) => {
  let index = selectAll.value.findIndex((item: any) => {
    return item.ticketId === e.id;
  });

  let params = {
    isOrderQuantity: null,
    sellingPrice: null,
    orderQuantity: null,
    'stockInfo.availableStock': null,
    validType: null,
    validBeginDate: null,
    validEndDate: null,
    validDayNum: null,
    disabled: null,
  };
  for (const key in params) {
    selectAll.value[index][key] = e[key];
    selectAll2.value[index][key] = e[key];
  }

  if (e.isReservation && !e.periodList.length) {
    selectAll.value[index].disabled = 1;
    selectAll2.value[index].disabled = 1;
  }
});

// ========================================================================================
const getInfoData = async () => {
  await getTypeList();
  await getList();
};
getInfoData();

// ========================================================================================
const clearBtn = () => {
  confirm('清空后所选数据将全部清空，是否确认继续？')
    .then(() => {
      selectAll.value = [];
      selectAll2.value = [];
    })
    .catch(() => {});
};
</script>

<template>
  <div class="flex h-full min-h-full w-full justify-between gap-4">
    <!-- left -->
    <div class="bg-color-white flex w-2/5 flex-col rounded-lg pt-4">
      <Space direction="vertical" class="w-full px-4">
        <div class="flex w-full items-center justify-between gap-4">
          <InputSearch
            v-model:value="getListParams.name"
            placeholder="请输入门票名称"
            size="large"
            allowClear
            @search="getList"
          />
          <DatePicker
            v-model:value="currentDate"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            :allowClear="false"
            placeholder="请选择日期"
            size="large"
            class="w-full"
            @change="searchTime"
          />
        </div>

        <Tabs v-model:activeKey="getListParams.typeId" @change="typeBtn">
          <TabPane
            v-for="item in typeList"
            :key="item.id"
            :tab="item.typeName"
          />
        </Tabs>
      </Space>

      <div class="flex-1 overflow-hidden overflow-y-auto px-4">
        <listItemTem
          v-if="listData && listData.length"
          :list="listData"
          :selectAll="selectAll"
          @listSelect="listSelect"
        />
        <div v-else class="pt-10">
          <Empty />
        </div>
      </div>
    </div>

    <!-- right -->
    <div class="bg-color-white flex flex-1 flex-col rounded-lg">
      <div class="flex items-center justify-between border-b px-4 py-2">
        <div class="text-lg">已选门票</div>
        <div class="flex-center gap-3">
          <Button type="primary" danger @click="clearBtn">清空</Button>
          <Button>挂单</Button>
          <Button type="primary">下单</Button>
        </div>
      </div>
      <div class="flex-1 overflow-hidden overflow-y-auto p-2">
        <buyInfoTem
          v-if="selectAll2 && selectAll2.length"
          :searchInfo="getListParams"
          :selectAll="selectAll2"
        />
        <div v-else class="pt-10">
          <Empty />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
