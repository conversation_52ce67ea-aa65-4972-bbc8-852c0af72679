// 置灰
.setGreyOut {
  filter: grayscale(1);
}

// 文本颜色
.text-color-white {
  color: #fff;
}
.text-color-black {
  color: #000;
}
.text-color-primary {
  color: hsl(var(--primary));
}
.text-color-success {
  color: hsl(var(--success));
}
.text-color-warning {
  color: hsl(var(--warning));
}
.text-color-error {
  color: hsl(var(--destructive));
}
.text-color-info {
  color: hsl(var(--muted-foreground));
}

// 背景颜色
.bg-color-white {
  background-color: hsl(var(--header));
}
.bg-color-black {
  background-color: #000;
}
.bg-color-quickBox {
  background-color: hsl(var(--background-deep));
}
.bg-color-primary {
  background-color: hsl(var(--primary));
}
.bg-color-success {
  background-color: hsl(var(--success));
}
.bg-color-warning {
  background-color: hsl(var(--warning));
}
.bg-color-error {
  background-color: hsl(var(--destructive));
}
.bg-color-info {
  background-color: hsl(var(--muted-foreground));
}

// 边框颜色border
.border-color-primary {
  border-color: hsl(var(--primary));
}
.border-color-success {
  border-color: hsl(var(--success));
}
.border-color-warning {
  border-color: hsl(var(--warning));
}
.border-color-error {
  border-color: hsl(var(--destructive));
}
.border-color-info {
  border-color: hsl(var(--muted-foreground));
}

// 设置超出显示省略号
@for $i from 1 through 10 {
  .ellipsis-#{$i} {
    display: -webkit-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-line-clamp: #{$i};
    -webkit-box-orient: vertical !important;
  }
}
