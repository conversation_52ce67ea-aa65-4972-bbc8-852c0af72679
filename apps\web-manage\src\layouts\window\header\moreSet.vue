<script lang="ts" setup>
import type { MenuProps } from 'ant-design-vue';

import { ref } from 'vue';

// ============================================================================
import {
  KeyOutlined,
  LogoutOutlined,
  MenuOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';
import { Dropdown, Menu, MenuDivider, MenuItem, message } from 'ant-design-vue';

import { useAuthStore } from '#/store';

import userInfoTem from './components/userInfo.vue';
import passwordTem from './components/password.vue';

const userInfoTemRef: any = ref(null);
const passwordTemRef: any = ref(null);

const authStore = useAuthStore();

// ======================================================================================================================
const handleMenuClick: MenuProps['onClick'] = (e: any) => {
  menuBtn(e.key);
};
const menuBtn = (key: string) => {
  switch (key) {
    case 'info': {
      userInfoTemRef.value.open();
      break;
    }
    case 'password': {
      passwordTemRef.value.open();
      break;
    }
    case 'logout': {
      authStore.logout();
      break;
    }
    case 'setting': {
      message.warning('功能正在开发中...');
      break;
    }
  }
};
</script>

<template>
  <div>
    <Dropdown placement="bottomRight" arrow>
      <div class="flex-center mr-2 cursor-pointer pr-2" @click.prevent>
        <MenuOutlined />
      </div>
      <template #overlay>
        <Menu @click="handleMenuClick">
          <MenuItem key="info">
            <UserOutlined />
            修改信息
          </MenuItem>
          <MenuItem key="password">
            <KeyOutlined />
            修改密码
          </MenuItem>
          <MenuItem key="setting">
            <SettingOutlined />
            系统设置
          </MenuItem>
          <MenuDivider />
          <MenuItem key="logout">
            <LogoutOutlined />
            退出登录
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </div>

  <!-- 个人中心 -->
  <userInfoTem ref="userInfoTemRef" />
  <!-- 修改密码 -->
  <passwordTem ref="passwordTemRef" />
</template>

<style scoped lang="scss"></style>
