<script lang="ts" setup>
import { Tooltip } from 'ant-design-vue';
let props = defineProps(['list']);

// =====================================================================================
const isCurrentTimePeriod = (e: any) => {
  // 获取当前时间（格式为 "HH:mm"）
  const now = new Date();
  const pad = (n: number) => n.toString().padStart(2, '0');
  const current = `${pad(now.getHours())}:${pad(now.getMinutes())}`;

  // 比较时间字符串e
  if (e.beginTime <= current && current <= e.endTime) {
    return 'text-color-warning border-color-warning ';
  }
  return '';
};
</script>

<template>
  <div class="mb-3 overflow-hidden">
    <div class="overflow-hidden" style="margin: -0.5rem 0 0 -0.5rem">
      <div
        v-for="item in list"
        class="float-left ml-2 mt-2 cursor-pointer rounded-lg border px-2 py-1"
        :class="[
          item.stockInfo.availableStock === 0
            ? 'bg-color-quickBox cursor-no-drop'
            : '',
          isCurrentTimePeriod(item),
        ]"
      >
        <Tooltip
          :title="
            isCurrentTimePeriod(item)
              ? `请注意，此时段 ${item.endTime} 后到期`
              : ''
          "
          color="red"
        >
          <div>{{ item.beginTime }}~{{ item.endTime }}</div>
          <div class="flex items-center justify-center gap-1 text-xs">
            <div>库存:</div>
            <div v-if="item.stockInfo.availableStock === -1">不限</div>
            <div v-else>
              {{
                item.stockInfo.availableStock > 1000
                  ? '999+'
                  : item.stockInfo.availableStock
              }}
            </div>
          </div>
        </Tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
