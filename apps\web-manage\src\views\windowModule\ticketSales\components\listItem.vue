<script lang="ts" setup>
import { useAccessStore } from '@vben/stores';
const { accessAllEnumsMap } = useAccessStore();

let props = defineProps(['list', 'selectAll']);

// ==========================================================================
let emits = defineEmits(['listSelect']);
const itemBtn = (e: any) => {
  if (!e.stockInfo.availableStock || e.disabled) return;
  emits('listSelect', e);
};

// =================================================================================
const setClass: any = (e: any) => {
  let str = 'cursor-pointer';
  if (!e.stockInfo.availableStock || e.disabled) {
    str = 'bg-color-quickBox cursor-no-drop';
  }

  let exists = props.selectAll.some(
    (item: any) => JSON.stringify(item) === JSON.stringify(e),
  );
  if (exists) {
    str += ' border-color-primary border-2';
  }
  return str;
};
</script>

<template>
  <div v-for="(item, index) in props.list" :key="index">
    <div
      class="mb-4 flex items-center justify-between gap-3 overflow-hidden rounded-lg border"
      :class="setClass(item)"
      @click="itemBtn(item)"
    >
      <div class="flex-center relative">
        <img
          v-if="item.ticketCover"
          :src="item.ticketCover"
          :alt="item.ticketName"
          class="h-32 w-32 object-cover"
        />
        <div
          class="absolute bottom-1 right-2 rounded-lg px-2 text-white"
          style="background: rgba(0, 0, 0, 0.2)"
        >
          <span v-if="item.stockInfo.availableStock === -1">不限</span>
          <span v-else-if="item.stockInfo.availableStock === 0">无</span>
          <span v-else="item.stockInfo.availableStock">
            {{
              item.stockInfo.availableStock > 1000
                ? '999+'
                : item.stockInfo.availableStock
            }}
          </span>
        </div>
      </div>
      <div class="flex-1">
        <div class="ellipsis-1 flex flex-1 text-base font-bold">
          ({{ accessAllEnumsMap.ticketModel[item.model] }})
          {{ item.ticketName }}
        </div>
        <div class="relative mt-2">
          <div class="flex flex-col gap-1.5 text-xs">
            <div class="flex items-center gap-1">
              <span class="text-color-info">有效期:</span>
              <span v-if="item.validType === 1">
                {{ accessAllEnumsMap.ticketValidType[item.validType] }}
              </span>
              <span v-else-if="item.validType === 5">
                {{ item.validBeginDate }}~{{ item.validEndDate }}有效
              </span>
              <span v-else>
                {{
                  accessAllEnumsMap.ticketValidType[item.validType].replace(
                    'X',
                    item.validDayNum,
                  )
                }}
              </span>
            </div>
            <div class="flex items-center gap-1">
              <span class="text-color-info">二维码类型:</span>
              <span>
                {{ accessAllEnumsMap.tickeQrcodeRule[item.qrcodeRule] }}
              </span>
            </div>
            <div class="flex items-center gap-1">
              <span class="text-color-info">实名制:</span>
              <span>
                {{
                  accessAllEnumsMap.tickeAuthenticationType[
                    item.authenticationType
                  ]
                }}
              </span>
            </div>
            <div class="flex items-center gap-1">
              <span class="text-color-info">限购:</span>
              <span>
                {{ !item.isOrderQuantity ? '不限购' : item.orderQuantity }}
              </span>
            </div>
          </div>
          <div class="text-color-error absolute bottom-0 right-2 text-xl">
            ￥{{ item.sellingPrice }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
