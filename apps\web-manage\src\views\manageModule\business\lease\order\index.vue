<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { Page } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getRentalOrderList } from '#/api/manageModule';
import { useGridFormSchema, useColumns } from './data';
const { accessAllEnums } = toRefs(useAccessStore());
import { Button, Table } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { router } from '#/router';

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  console.log(code, row);
};
const orderList = ref([]);
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['dateRange', ['beginDate', 'endDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    border: 'inner',
    pagerConfig: {
      pageSize: 10,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getRentalOrderList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          //循环数据
          res.list.forEach((item: any) => {
            item.isParent = true;
            if (item.orderItemList && item.orderItemList.length) {
              item.orderItemList.forEach((item2: any, index: number) => {
                item2.isParent = false;
                item2.orderId = item.id; // 添加订单ID引用
                item2.itemIndex = index; // 添加项目索引
                item2.orderTime = item.orderTime;
                item2.rentalPriceTotal = item.rentalPrice;
                item2.damagePriceTotal = item.damagePrice;
                item2.depositPriceTotal = item.depositPrice;
                item2.orderType = item.orderType;
                item2.payStatus = item.payStatus;
                item2.orderStatus = item.orderStatus;
                item2.payMethod = item.payMethod;
                item2.orderSource = item.orderSource;
              });
            }
          });
          setAllExpand(true);
          orderList.value = res.list;
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    virtualXConfig: {
      enabled: false,
    },
    virtualYConfig: {
      enabled: false,
    },
    spanMethod({ row, column }) {
      if (row.orderItemList && row.orderItemList.length) {
        if (column.field === 'goodsName') {
          return { rowspan: 1, colspan: 14 };
        }
      }
      return { rowspan: 1, colspan: 1 };
    },
    expandConfig: {
      mode: 'inside',
      expandAll: true,
      reserve: true,
    },
    treeConfig: {
      rowField: 'id',
      parentField: 'orderId',
      childrenField: 'orderItemList',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const handleInfo = (row: any) => {
  router.push({
    name: 'leaseOrderDetail',
    query: { id: row.id },
  });
};
const setAllExpand = (value: any) => {
  setTimeout(() => {
    gridApi.grid.setAllRowExpand(value);
  }, 50);
  // setTimeout(() => {
  //   gridApi.grid.setRowExpand(orderList.value, true);
  // }, 50);
};
const filterSource = (orderSource: any) => {
  return accessAllEnums.value?.orderSource.list.find(
    (item: any) => item.value === orderSource,
  )?.label;
};

// 需要合并单元格的字段
const mergeFields = [
  'orderTime',
  'depositPriceTotal',
  'rentalPriceTotal',
  'damagePriceTotal',
  'payStatus',
  'orderStatus',
  'payMethod',
  'options',
];

// 单元格合并函数
const getMergeProps = (
  field: string,
  index: number | undefined,
  dataSource: any[],
) => {
  // 只有需要合并的字段才进行合并处理
  if (!mergeFields.includes(field) || index === undefined) {
    return {};
  }

  // 如果是第一行，则显示合并的单元格
  if (index === 0) {
    return {
      rowSpan: dataSource.length,
    };
  }

  // 其他行不显示
  return {
    rowSpan: 0,
  };
};

const columns: TableColumnType[] = [
  {
    title: '物品信息',
    dataIndex: 'goodsName',
    fixed: 'left',
    minWidth: 400,
    customRender: ({ record }: any) => {
      return record.goodsName || '--';
    },
  },
  {
    title: '计费方式',
    dataIndex: 'billingMethod',
    width: 120,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.billingMethod === 1
        ? '按次计费'
        : record.billingMethod === 2
          ? '按分钟计费'
          : '按小时计费';
    },
  },
  {
    title: '租金',
    dataIndex: 'rentalPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '押金',
    dataIndex: 'depositPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '租赁数量',
    dataIndex: 'rentalNum',
    width: 120,
    align: 'center',
  },
  {
    title: '下单时间',
    dataIndex: 'orderTime',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      // 使用记录中的 itemIndex 而不是表格的 index
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'orderTime',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '损坏补费',
    dataIndex: 'damagePrice',
    width: 120,
    align: 'center',
  },
  {
    title: '租金总额',
    dataIndex: 'rentalPriceTotal',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'rentalPriceTotal',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '押金总额',
    dataIndex: 'depositPriceTotal',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'depositPriceTotal',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '损坏补费总额',
    dataIndex: 'damagePriceTotal',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'damagePriceTotal',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '支付状态',
    dataIndex: 'payStatus',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'payStatus',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderPayStatus.list.find(
        (item: any) => item.value === record.payStatus,
      )?.label;
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'orderStatus',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.rentalOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
  },
  {
    title: '支付方式',
    dataIndex: 'payMethod',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'payMethod',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderPayMethod.list.find(
        (item: any) => item.value === record.payMethod,
      )?.label;
    },
  },
  {
    title: '',
    dataIndex: 'options',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder: any = orderList.value.find(
        (item: any) => item.id === record.orderId,
      );
      return getMergeProps(
        'options',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
];
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #expand_header="{ row }">
        <div v-if="row.isParent" class="flex items-center justify-between">
          <div>
            <span>ID：{{ row.id }}</span>
            <span class="ml-5">订单号：{{ row.orderNo }}</span>
            <span class="ml-5"
              >订单来源：{{ filterSource(row.orderSource) }}</span
            >
            <!-- <span class="ml-5">景区：{{ row.scenicInfo.scenicName }}</span> -->
          </div>
          <div class="flex w-[105px] justify-center gap-2">
            <Button type="link" size="small" @click="handleInfo(row)"
              >详情</Button
            >
          </div>
        </div>
        <div v-else>{{ row.goodsName }}</div>
      </template>
      <template #expand_content="{ row }">
        <Table
          :key="row.id"
          :columns="columns"
          :data-source="row.orderItemList"
          :pagination="false"
          :show-header="false"
          :scroll="{ x: true }"
          :row-key="(record: any) => record.id || record.ticketId"
          :bordered="true"
          class="expand-table"
        ></Table>
      </template>
    </Grid>
  </Page>
</template>

<style scoped>
.expand-table {
  margin: 8px 0px;
}

.expand-table :deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}
.expand-table :deep(.ant-table-container) {
  border-start-start-radius: 0px !important;
  border-start-end-radius: 0px !important;
}
.expand-table :deep(.ant-table-container table) {
  border-radius: 0px !important;
}
.expand-table :deep(.ant-table-tbody > tr > td:last-child[rowspan]) {
  border-right: none !important;
}
</style>
