<script lang="ts" setup>
import { useRouter } from 'vue-router';
const router = useRouter();

import type { MenuProps } from 'ant-design-vue';

import { confirm } from '@vben/common-ui';
import { MenuBadge } from '@vben-core/menu-ui';

import { ref } from 'vue';

import { useUserStore } from '@vben/stores';

const userStore = useUserStore();
const userInfo: any = ref(userStore.userInfo || {});

import { SwapOutlined } from '@ant-design/icons-vue';
import { Dropdown, Menu, MenuItem } from 'ant-design-vue';

import { changeOffice } from '#/api/windowModule/index';

// 售票点切换 ======================================================================================================================
const handleMenuClick: MenuProps['onClick'] = (e: any) => {
  confirm(`是否确认切换售票点【${e.key.pointName}】？`)
    .then(async () => {
      await changeOffice({ officeId: e.key.id });
      router.push('/');
      setTimeout(() => {
        location.reload();
      }, 50);
    })
    .catch(() => {});
};
</script>

<template>
  <div
    v-if="
      userInfo.officeInfo?.lastLoginOfficeInfo &&
      userInfo.officeInfo?.officeList.length
    "
  >
    <Dropdown placement="bottomRight">
      <div>
        {{ userInfo.officeInfo?.lastLoginOfficeInfo.scenicInfo.scenicName }}
        -
        {{ userInfo.officeInfo?.lastLoginOfficeInfo.pointName }}
        <SwapOutlined />
      </div>
      <template #overlay>
        <Menu @click="handleMenuClick">
          <MenuItem
            v-for="item in userInfo.officeInfo.officeList"
            :key="item"
            :disabled="userInfo.officeInfo?.lastLoginOfficeId === item.id"
          >
            <div class="flex items-center">
              <div
                class="flex-center px-2"
                v-if="userInfo.officeInfo?.lastLoginOfficeId === item.id"
              >
                <MenuBadge
                  v-bind="{ badgeType: 'dot', badgeVariants: 'success' }"
                />
              </div>
              <div>{{ item.scenicInfo.scenicName }} - {{ item.pointName }}</div>
            </div>
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </div>
</template>

<style scoped lang="scss"></style>
