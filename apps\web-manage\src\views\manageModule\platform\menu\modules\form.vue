<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '#/adapter/form';

import { computed, h, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { getPopupContainer, isHttpUrl } from '@vben/utils';

import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';

import { useVbenForm, z } from '#/adapter/form';
import {
  createMenu,
  getMenuList,
  isMenuNameExists,
  isMenuPathExists,
  SystemMenuApi,
  updateMenu,
} from '#/api/manageModule/system/menu';
import { $t } from '#/locales';
import { componentKeys } from '#/router/routes';

import { getMenuTypeOptions, getRequestMethodOptions } from '../data';

const emit = defineEmits<{
  success: [];
}>();
const formData = ref<SystemMenuApi.SystemMenu>();
const loading = ref(false);
const titleSuffix = ref<string>();
const schema: VbenFormSchema[] = [
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getMenuTypeOptions(),
      optionType: 'button',
    },
    defaultValue: 'menu',
    fieldName: 'type',
    formItemClass: 'col-span-2 md:col-span-2',
    label: $t('system.menu.type'),
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: $t('system.menu.menuName'),
    rules: z
      .string()
      .min(2, $t('ui.formRules.minLength', [$t('system.menu.menuName'), 2]))
      .max(30, $t('ui.formRules.maxLength', [$t('system.menu.menuName'), 30]))
      .refine(
        async (value: string) => {
          if (!value) {
            return false;
          }
          return !(await isMenuNameExists(
            value,
            formData.value?.id,
            module.value,
          ));
        },
        (value) => ({
          message: $t('ui.formRules.alreadyExists', [
            $t('system.menu.menuName'),
            value,
          ]),
        }),
      ),
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getMenuList,
      class: 'w-full',
      filterTreeNode(input: string, node: Recordable<any>) {
        if (!input || input.length === 0) {
          return true;
        }
        const title: string = node.meta?.title ?? '';
        if (!title) return false;
        return title.includes(input) || $t(title).includes(input);
      },
      getPopupContainer,
      labelField: 'meta.title',
      showSearch: true,
      treeDefaultExpandAll: true,
      valueField: 'id',
      childrenField: 'children',
      allowClear: true,
    },
    fieldName: 'parentId',
    label: $t('system.menu.parent'),
    renderComponentContent() {
      return {
        title({ label, meta }: { label: string; meta: Recordable<any> }) {
          const coms = [];
          if (!label) return '';
          if (meta?.icon) {
            coms.push(h(IconifyIcon, { class: 'size-4', icon: meta.icon }));
          }
          coms.push(h('span', { class: '' }, $t(label || '')));
          return h('div', { class: 'flex items-center gap-1' }, coms);
        },
      };
    },
  },
  {
    component: 'Input',
    // componentProps() {
    //   // 不需要处理多语言时就无需这么做
    //   return {
    //     addonAfter: titleSuffix.value,
    //     onChange({ target: { value } }: ChangeEvent) {
    //       titleSuffix.value = value && $te(value) ? $t(value) : undefined;
    //     },
    //   };
    // },
    fieldName: 'meta.title',
    label: $t('system.menu.menuTitle'),
    rules: 'required',
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['catalog', 'embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'path',
    label: $t('system.menu.path'),
    rules: z
      .string()
      .min(2, $t('ui.formRules.minLength', [$t('system.menu.path'), 2]))
      .max(100, $t('ui.formRules.maxLength', [$t('system.menu.path'), 100]))
      .refine(
        (value: string) => {
          return value.startsWith('/');
        },
        $t('ui.formRules.startWith', [$t('system.menu.path'), '/']),
      )
      .refine(
        async (value: string) => {
          if (!value) {
            return false;
          }
          return !(await isMenuPathExists(
            value,
            formData.value?.id,
            module.value,
          ));
        },
        (value) => ({
          message: $t('ui.formRules.alreadyExists', [
            $t('system.menu.path'),
            value,
          ]),
        }),
      ),
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'activePath',
    help: $t('system.menu.activePathHelp'),
    label: $t('system.menu.activePath'),
    rules: z
      .string()
      .max(100, $t('ui.formRules.maxLength', [$t('system.menu.path'), 100]))
      .refine(
        (value: string) => {
          if (!value) {
            return true;
          }
          return value.startsWith('/');
        },
        $t('ui.formRules.startWith', [$t('system.menu.path'), '/']),
      )
      .refine(async (value: string) => {
        if (!value) {
          return true;
        }
        return await isMenuPathExists(value, formData.value?.id, module.value);
        // return false;
      }, $t('system.menu.activePathMustExist'))
      .optional(),
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.query',
    label: $t('system.menu.query'),
  },
  {
    component: 'RadioGroup',
    fieldName: 'iconType',
    label: '图标类型',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: '图标', value: 1 },
        { label: '自定义', value: 2 },
      ],
      optionType: 'button',
    },
    defaultValue: 1,
    dependencies: {
      show: (values) => {
        return ['catalog', 'embedded', 'link', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
  },
  {
    component: 'IconPicker',
    componentProps: {
      prefix: 'carbon',
    },
    dependencies: {
      show: (values) => {
        return (
          ['catalog', 'embedded', 'link', 'menu'].includes(values.type) &&
          values.iconType === 1
        );
      },
      triggerFields: ['type', 'iconType'],
    },
    fieldName: 'meta.icon',
    label: $t('system.menu.icon'),
  },
  {
    component: 'CusUpload',
    componentProps: {
      maxCount: 1,
      accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
      fileTypeTag: 'menu',
      multiple: false,
      showUploadList: true,
      listType: 'picture-card',
    },
    dependencies: {
      show: (values) => {
        return (
          ['catalog', 'embedded', 'menu'].includes(values.type) &&
          values.iconType === 2
        );
      },
      triggerFields: ['type', 'iconType'],
    },
    fieldName: 'meta.iconUrl',
    label: $t('system.menu.icon'),
  },
  {
    component: 'AutoComplete',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      filterOption(input: string, option: { value: string }) {
        return option.value.toLowerCase().includes(input.toLowerCase());
      },
      options: componentKeys.map((v) => ({ value: v })),
    },
    dependencies: {
      rules: (values) => {
        return values.type === 'menu' ? 'required' : null;
      },
      show: (values) => {
        return values.type === 'menu';
      },
      triggerFields: ['type'],
    },
    fieldName: 'component',
    label: $t('system.menu.component'),
  },
  {
    component: 'InputNumber',
    help: '数字越大，排序越靠前',
    componentProps: {
      class: 'w-full',
      max: 10000,
      defaultValue: 0,
    },
    dependencies: {
      show: (values) => {
        return !['button'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.listorder',
    label: $t('system.menu.listorder'),
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['embedded', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'linkSrc',
    label: $t('system.menu.linkSrc'),
    rules: z.string().url($t('ui.formRules.invalidURL')),
  },
  {
    component: 'RadioGroup',
    dependencies: {
      show: (values) => {
        return ['button'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    componentProps: {
      buttonStyle: 'solid',
      options: getRequestMethodOptions(),
      optionType: 'button',
    },
    defaultValue: 'get',
    fieldName: 'method',
    label: $t('system.menu.method'),
  },
  {
    component: 'Input',
    dependencies: {
      show: (values) => {
        return ['button'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'apiPath',
    label: $t('system.menu.apiPath'),
  },
  {
    component: 'Input',
    dependencies: {
      componentProps(values: any) {
        if (values.apiPath && values.method) {
          values.authCode = `${values.method}${values.apiPath.replaceAll(/\/+/g, ':')}`;
        }
        return {};
      },
      rules: (values) => {
        return values.type === 'button' ? 'required' : null;
      },
      show: (values) => {
        return ['button', 'catalog', 'embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type', 'apiPath', 'method'],
    },
    fieldName: 'authCode',
    label: $t('system.menu.authCode'),
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: $t('common.enabled'), value: 1 },
        { label: $t('common.disabled'), value: 0 },
      ],
      optionType: 'button',
    },
    defaultValue: 1,
    fieldName: 'status',
    label: $t('system.menu.status'),
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      options: [
        { label: $t('system.menu.badgeType.dot'), value: 'dot' },
        { label: $t('system.menu.badgeType.normal'), value: 'normal' },
      ],
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'button';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badgeType',
    label: $t('system.menu.badgeType.title'),
  },
  {
    component: 'Input',
    componentProps: (values) => {
      return {
        allowClear: true,
        class: 'w-full',
        disabled: values.meta?.badgeType !== 'normal',
      };
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'button';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badge',
    label: $t('system.menu.badge'),
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      options: SystemMenuApi.BadgeVariants.map((v) => ({
        label: v,
        value: v,
      })),
    },
    dependencies: {
      show: (values) => {
        return values.type !== 'button';
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.badgeVariants',
    label: $t('system.menu.badgeVariants'),
  },
  {
    component: 'Divider',
    dependencies: {
      show: (values) => {
        return !['button', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'divider1',
    formItemClass: 'col-span-2 md:col-span-2 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => $t('system.menu.advancedSettings'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.keepAlive',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.keepAlive'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['embedded', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.affixTab',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.affixTab'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInMenu',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.hideInMenu'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return ['catalog', 'menu'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideChildrenInMenu',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.hideChildrenInMenu'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInBreadcrumb',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.hideInBreadcrumb'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button', 'link'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.hideInTab',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.hideInTab'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button', 'catalog'].includes(values.type);
      },
      triggerFields: ['type'],
    },
    fieldName: 'meta.openInNewWindow',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.openInNewWindow'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button'].includes(values.type);
      },
      triggerFields: ['type', 'parentId'],
    },
    fieldName: 'meta.noBasicLayout',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.noBasicLayout'),
      };
    },
  },
  {
    component: 'Checkbox',
    dependencies: {
      show: (values) => {
        return !['button'].includes(values.type) && !values.parentId;
      },
      triggerFields: ['type', 'parentId'],
    },
    fieldName: 'meta.noNeedPermission',
    renderComponentContent() {
      return {
        default: () => $t('system.menu.noNeedPermission'),
      };
    },
  },
];

const breakpoints = useBreakpoints(breakpointsTailwind);
const isHorizontal = computed(() => breakpoints.greaterOrEqual('md').value);
const module = ref('manage');

const [Form, formApi] = useVbenForm({
  commonConfig: {
    colon: true,
    formItemClass: 'col-span-2 md:col-span-1',
  },
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [Drawer, drawerApi] = useVbenDrawer({
  onBeforeClose() {
    if (loading.value) return false;
  },
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemMenuApi.SystemMenu>();
      module.value = data.module;
      if (data?.type === 'link') {
        data.linkSrc = data.meta?.link;
      } else if (data?.type === 'embedded') {
        data.linkSrc = data.meta?.iframeSrc;
      }
      if (data) {
        formData.value = data;
        if (data.meta) {
          if (!isHttpUrl(data.meta?.icon)) {
            formData.value.iconType = 1;
          } else {
            formData.value.iconType = 2;
            // 确保meta对象存在
            if (!formData.value.meta) {
              formData.value.meta = {};
            }
            // 添加iconUrl属性到meta对象
            (formData.value.meta as any).iconUrl = data.meta?.icon
              ?.split(',')
              .map((item: any) => {
                return {
                  uid: item,
                  name: item,
                  status: 'done',
                  url: item,
                  response: {
                    url: item,
                  },
                };
              });
          }
        }
        formApi.setValues(formData.value);
        titleSuffix.value = formData.value.meta?.title
          ? $t(formData.value.meta.title)
          : '';
      } else {
        formApi.resetForm();
        titleSuffix.value = '';
      }
    }
  },
});

async function onSubmit() {
  const { valid } = await formApi.validate();
  if (valid) {
    loading.value = true;
    drawerApi.setState({
      closeOnClickModal: false,
      closeOnPressEscape: false,
      confirmLoading: true,
      loading: true,
    });
    const data =
      await formApi.getValues<
        Omit<SystemMenuApi.SystemMenu, 'children' | 'id'>
      >();
    if (data.type === 'link') {
      data.meta = { ...data.meta, link: data.linkSrc };
    } else if (data.type === 'embedded') {
      data.meta = { ...data.meta, iframeSrc: data.linkSrc };
    }
    delete data.linkSrc;

    // 处理图标逻辑
    if (data.iconType === 2) {
      // 自定义图标：如果有上传的图标URL，使用上传的URL；否则使用已设置的icon值
      if (data.meta?.iconUrl && Array.isArray(data.meta.iconUrl) && data.meta.iconUrl.length > 0) {
        data.meta.icon = data.meta.iconUrl.map((item: any) => item.url || item.response?.url).join(',');
      }
      // 清理iconUrl字段，不需要提交到后端
      if (data.meta?.iconUrl) {
        delete (data.meta as any).iconUrl;
      }
    } else if (data.iconType === 1) {
      // 图标选择器：清理可能存在的iconUrl字段
      if (data.meta?.iconUrl) {
        delete (data.meta as any).iconUrl;
      }
    }

    // 清理iconType字段，不需要提交到后端
    delete (data as any).iconType;
    try {
      await (formData.value?.id
        ? updateMenu(formData.value.id, { ...data, module: module.value })
        : createMenu({ ...data, module: module.value }));
      setTimeout(() => {
        drawerApi.close();
        emit('success');
      }, 100);
    } finally {
      loading.value = false;
      drawerApi.setState({
        closeOnClickModal: true,
        closeOnPressEscape: true,
        confirmLoading: false,
        loading: false,
      });
    }
  }
}
const getDrawerTitle = computed(() =>
  formData.value?.id
    ? $t('ui.actionTitle.edit', [$t('system.menu.name')])
    : $t('ui.actionTitle.create', [$t('system.menu.name')]),
);
</script>

<template>
  <Drawer class="w-full max-w-[900px]" :title="getDrawerTitle">
    <Form class="mx-4" :layout="isHorizontal ? 'horizontal' : 'vertical'" />
  </Drawer>
</template>
