<script lang="ts" setup>
import { InputGroup, Input, Button } from 'ant-design-vue';

let props = defineProps(['row']);
</script>

<template>
  <div class="flex gap-3">
    <div class="flex w-1/2 items-center">
      <div class="mr-2">游客:</div>
      <Input
        class="flex-1"
        v-model:value="row.username"
        placeholder="请输入游客姓名"
        allowClear
      />
    </div>
    <div class="flex w-1/2 items-center">
      <div class="mr-2">证件号:</div>
      <InputGroup compact class="!flex flex-1">
        <Input
          v-model:value="row.value19"
          placeholder="请输入证件号"
          allowClear
        />
        <Button>读取</Button>
      </InputGroup>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
