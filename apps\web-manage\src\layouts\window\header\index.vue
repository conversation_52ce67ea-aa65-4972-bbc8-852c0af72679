<script lang="ts" setup>
import { onUnmounted, ref, watch } from 'vue';

// ============================================================================
import { useUserStore } from '@vben/stores';

import { Avatar } from 'ant-design-vue';

import switchTicketTem from './switchTicket.vue';
import moreSetTem from './moreSet.vue';

const userStore = useUserStore();
const userInfo: any = ref({});
watch(
  () => userStore.userInfo,
  (newVal) => {
    userInfo.value = newVal;
  },
  { deep: true, immediate: true },
);

// 年月日时分秒 ======================================================================================================================
const currentTime = ref('');
const timer: any = ref(null);
const updateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  // 更新页面显示
  currentTime.value = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;

  // 动态计算与下一秒的时间差（精准触发）
  const delay = 1000 - now.getMilliseconds();
  setTimeout(updateTime, delay);
};
updateTime();
timer.value = setInterval(updateTime, 1000);
onUnmounted(() => {
  clearInterval(timer.value);
});
</script>

<template>
  <div class="text-color-white flex items-center justify-between py-2">
    <div class="flex-center" v-if="userInfo?.tenantInfo">
      <Avatar
        :src="userInfo.tenantInfo?.tenantLogo"
        shape="square"
        :size="30"
      />
      <div class="ml-2">{{ userInfo.tenantInfo?.tenantName }}</div>
      <div class="ml-2">{{ currentTime }}</div>
    </div>

    <div class="flex items-center gap-6">
      <switchTicketTem />

      <div class="flex-center">
        <Avatar v-if="userInfo?.avatar" :src="userInfo.avatar" :size="26" />
        <Avatar v-else :size="26">{{ userInfo?.name.charAt(0) }}</Avatar>
        <div class="ml-1">{{ userInfo?.name }}</div>
      </div>

      <moreSetTem />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
