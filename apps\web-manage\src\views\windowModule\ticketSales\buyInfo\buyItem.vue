<script lang="ts" setup>
import eventBus from '#/utils/eventBus';
import { InputNumber, message } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
const { accessAllEnumsMap } = useAccessStore();

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ref, watch, nextTick, inject } from 'vue';

let props = defineProps(['searchInfo', 'itemData']);

import listTimeTem from './listTime.vue';
import listIdCarTem from './listIdCar.vue';

// ==========================================================================
import { ticketInfo } from '#/api/windowModule/ticketSales';
let deleteBtn: any = inject('ticketSalesDelSelect');

let infoParams: any = ref({});
const getInfo = async () => {
  let params = {
    ticketId: props.itemData.ticketId,
    playDate: props.searchInfo.playDate,
  };
  let resData = await ticketInfo(params);
  infoParams.value = resData;

  eventBus.emit('ticketSalesListRefreshData', resData);

  // 分时预约，没有可预约的时间段
  if (resData.isReservation && !resData.periodList.length) {
    message.error('当前门票为分时预约票，当天已无可约场次！');
    deleteBtn(props.itemData);
  }

  nextTick(() => {
    if (gridApi && gridApi.grid && gridApi.grid.setAllRowExpand) {
      gridApi.grid.setAllRowExpand(true);
    }
  });
};
getInfo();

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    expandConfig: {
      expandAll: true,
      showIcon: false,
    },
    pagerConfig: {
      enabled: false,
    },
    columnConfig: {
      resizable: false,
    },
    align: 'left',
    minHeight: 100,
    columns: [
      { type: 'expand', width: 1, slots: { content: 'expand' } },
      { field: 'ticketName', title: '门票' },
      {
        field: 'model',
        title: '门票类型',
        width: 110,
        slots: { default: 'model' },
      },
      {
        field: 'ticketNum',
        title: '数量',
        width: 110,
        slots: { default: 'ticketNum' },
      },
      { field: 'sellingPrice', title: '价格', width: 110 },
      {
        field: 'qrcodeRule',
        title: '二维码类型',
        width: 110,
        slots: { default: 'qrcodeRule' },
      },
      {
        field: 'action',
        title: '操作',
        width: 40,
        align: 'center',
        slots: { default: 'action' },
      },
    ],
    data: [props.itemData],
  },
  gridEvents: {
    cellClick: ({ row }: any) => {
      console.log('🚀 ~ row:', row);
    },
  },
});

watch(
  () => props.itemData,
  () => {
    nextTick(() => {
      if (gridApi && gridApi.grid && gridApi.grid.setAllRowExpand) {
        gridApi.grid.setAllRowExpand(true);
      }
    });
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <Grid>
    <template #model="{ row }">
      {{ accessAllEnumsMap.ticketModel[row.model] }}
    </template>
    <template #ticketNum="{ row }">
      <InputNumber
        id="inputNumber"
        v-model:value="row.ticketNum"
        placeholder="请输入数量"
        :min="1"
        :max="!row.isOrderQuantity ? 9999 : row.orderQuantity"
        class="w-full"
      />
    </template>
    <template #qrcodeRule="{ row }">
      {{ accessAllEnumsMap.tickeQrcodeRule[row.qrcodeRule] }}
    </template>
    <template #action="{ row }">
      <div class="text-color-error cursor-pointer" @click="deleteBtn(row)">
        删除
      </div>
    </template>
    <template #expand="{ row }">
      <div class="p-3">
        <listTimeTem
          v-if="infoParams.periodList && infoParams.periodList.length"
          :list="infoParams.periodList"
        />
        <listIdCarTem :row="row" />
      </div>
    </template>
  </Grid>
</template>

<style scoped lang="scss"></style>
