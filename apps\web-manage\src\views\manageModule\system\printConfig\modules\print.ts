import { ref } from 'vue';
import { getLodop } from '#/utils/LodopFuncs';
import { printStyle as printStyleFn } from './printTemplate/generalTicket/printStyle';
import { message, Modal } from 'ant-design-vue';

// 添加打印防抖状态变量
const printTestDebounce = ref(false);
const printer = ref<any>(null);

const createPrint = (width: number) => {
  printer.value = getLodop(document.getElementById('printContent'), null);
  printer.value.PRINT_INIT('');
  printer.value.SET_PRINT_PAGESIZE(3, width + 'mm', '', '');
  printer.value.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Height:100%');
  printer.value.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Auto-Width');
  // 设置打印区域为100%以避免内容被缩放
  printer.value.ADD_PRINT_HTM(
    0,
    0,
    '100%',
    '90%',
    printStyleFn() +
      '<body>' +
      document.getElementById('printContent')?.innerHTML +
      '</body>',
  );
  printer.value.SET_LICENSES(
    '深圳市智络科技有限公司',
    'BBFF47D5AB0D522C0007D05CDE387E65',
    '',
    '',
  );
  console.log(
    printStyleFn() +
      '<body>' +
      document.getElementById('printContent')?.innerHTML +
      '</body>',
    '798',
  );
};

const print = (width: number) => {
  // 防抖，防止多次点击打印
  if (printTestDebounce.value) {
    return;
  }

  printTestDebounce.value = true;
  createPrint(width);
  printer.value.SET_PRINT_MODE('CATCH_PRINT_STATUS', true);
  if (printer.value.CVERSION) {
    // printer.value.PREVIEW(); // 预览
    printer.value.PRINT(); // 打印
    message.success('打印请求成功');

    // 3秒后重置防抖状态
    setTimeout(() => {
      printTestDebounce.value = false;
    }, 3000);
    return;
  } else {
    Modal.warning({
      title: '提示',
      content:
        '打印错误，打印异常/缺纸。请检查打印纸状态或联系工作人员，确认是否安装Web打印服务CLodop。完成后请刷新本页面重试。',
    });
    printTestDebounce.value = false;
  }
};

export { print };
