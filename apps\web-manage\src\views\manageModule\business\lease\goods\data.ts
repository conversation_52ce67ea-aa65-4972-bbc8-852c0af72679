import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { getAllScenicList, getAllGoodsTypetList } from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import { z } from '#/adapter/form';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'typeId',
      label: '类型',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.typeName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllGoodsTypetList,
        placeholder: '请选择类型',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '物品名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入物品名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'status',
      label: '物品状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入物品状态',
        allowClear: true,
        options: accessAllEnums.value.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'goodsName',
      title: '物品名称',
      minWidth: 150,
      fixed: 'left',
    },
    {
      field: 'goodsImage',
      title: '物品图片',
      width: 150,
      slots: { default: 'goodsImage' },
    },
    {
      field: 'scenicInfo',
      title: '所属景区',
      width: 150,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'goodsType',
      title: '物品类型',
      width: 150,
      formatter: ({ row }: any) => row.typeInfo.typeName || '--',
    },
    {
      field: 'billingMethod',
      title: '计费方式',
      width: 150,
      formatter: ({ row }: any) =>
        accessAllEnums.value.rentalBillingMethod.list.find(
          (item: any) => item.value == row.billingMethod,
        )?.label || '--',
    },
    {
      field: 'goodsPrice',
      title: '物品价值',
      width: 120,
    },
    {
      field: 'depositPrice',
      title: '押金',
      width: 120,
    },
    {
      field: 'totalStock',
      title: '物品库存数量',
      width: 120,
    },
    {
      field: 'availableNum',
      title: '可租数量',
      width: 120,
    },
    {
      field: 'rentedNum',
      title: '已租数量',
      width: 120,
    },
    {
      field: 'status',
      title: '物品状态',
      width: 150,
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'saleResource',
      title: '可售渠道',
      width: 120,
      slots: {
        default: 'CellSaleResource',
      },
    },
    {
      field: 'takeAddress',
      title: '取货地址',
      width: 150,
    },
    {
      field: 'remark',
      title: '备注',
      width: 150,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'goodsName',
          nameTitle: '物品名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'goodsName',
      label: '物品名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入物品名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'typeId',
      label: '物品类型',
      rules: 'selectRequired',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.typeName,
            value: item.id,
          }));
        },
        api: getAllGoodsTypetList,
        placeholder: '请选择物品类型',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      rules: 'selectRequired',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'takeAddress',
      label: '取货地址',
      rules: 'required',
      componentProps: {
        placeholder: '请输入取货地址',
        allowClear: true,
      },
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'goodsImage',
      label: '物品图片',
      componentProps: {
        maxCount: 9,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'goods',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传物品图片'),
    },
    {
      component: 'RadioGroup',
      fieldName: 'billingMethod',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      label: '计费方式',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择计费方式',
        allowClear: true,
        options: [
          {
            label: '按单次收费',
            value: 1,
          },
          {
            label: '按分钟收费',
            value: 2,
          },
          {
            label: '按小时收费',
            value: 3,
          },
        ],
      },
      defaultValue: 1,
    },
    {
      component: 'InputNumber',
      fieldName: 'frequencyPrice',
      label: '按次收费价格',
      rules: 'required',
      componentProps: {
        placeholder: '请输入按次收费价格',
        allowClear: true,
      },
      dependencies: {
        show: ({ billingMethod }) => billingMethod === 1,
        triggerFields: ['billingMethod'], // 监听字段
      },
    },
    {
      component: 'Input',
      fieldName: 'billingConf',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '阶梯计费配置',
      dependencies: {
        show: ({ billingMethod }) => billingMethod === 2 || billingMethod === 3,
        componentProps({ billingMethod }) {
          return {
            mode: billingMethod === 2 ? 'minute' : 'hour',
          };
        },
        triggerFields: ['billingMethod'], // 监听字段
      },
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'saleResource',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可售渠道',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择可售渠道',
        allowClear: true,
        options: accessAllEnums.value?.rentalSaleResource.list,
        mode: 'multiple',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'goodsPrice',
      label: '物品价值',
      rules: 'required',
      componentProps: {
        placeholder: '请输入物品价值',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'depositPrice',
      label: '押金',
      rules: 'required',
      componentProps: {
        placeholder: '请输入押金',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'totalStock',
      label: '物品库存数量',
      rules: 'required',
      componentProps: {
        placeholder: '请输入物品库存数量',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'rentedNum',
      label: '已租数量',
      rules: 'required',
      componentProps: {
        placeholder: '请输入已租数量',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'destroyNum',
      label: '损坏数量',
      rules: 'required',
      componentProps: {
        placeholder: '请输入损坏数量',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'status',
      label: '状态',
      rules: 'selectRequired',
      componentProps: {
        buttonStyle: 'solid',
        options: accessAllEnums.value?.status.list,
        optionType: 'default',
      },
      defaultValue: 1,
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
  ];
}
