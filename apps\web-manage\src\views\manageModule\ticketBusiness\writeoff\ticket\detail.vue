<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import {
  <PERSON>H<PERSON><PERSON>,
  Button,
  Card,
  Descriptions,
  DescriptionsItem,
  Table,
} from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { getOrderInfo, getVerificationInfo } from '#/api/manageModule';
import { useRoute } from 'vue-router';
import { ref, toRefs, onMounted } from 'vue';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());
import { useTabs } from '@vben/hooks';
const { setTabTitle } = useTabs();
import VerifyLog from './modules/verifyLog.vue';
import Verify from './modules/verify.vue';

const route = useRoute();
const orderId = ref<any>(route.query.id);
const orderInfo = ref<any>({});
const getOrderInfoData = async () => {
  const res = await getOrderInfo(orderId.value);
  orderInfo.value = res;
};

onMounted(() => {
  // 设置页面标签标题
  setTabTitle('门票核销订单详情-' + orderId.value);
  getOrderInfoData();
});

// 核销记录弹窗
const [VerifyLogModal, verifyLogModalApi] = useVbenModal({
  connectedComponent: VerifyLog,
  destroyOnClose: true,
});

const showVerifyLog = (orderDetailId: any, orderId: any, orderItemId: any) => {
  verifyLogModalApi
    .setData({
      orderDetailId,
      orderId,
      orderItemId,
    })
    .open();
};

// 核销弹窗
const [VerifyModal, verifyModalApi] = useVbenModal({
  connectedComponent: Verify,
  destroyOnClose: true,
});

// 核销
const handleVerify = (row: any) => {
  getVerificationInfo({ verificationCode: row.verificationCode })
    .then((res) => {
      verifyModalApi.setData(res).open();
    })
    .catch((err) => {
      console.log(err);
    });
};

const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};

const filterValidText = (val: any, data: any) => {
  let text = filterText(accessAllEnums.value.ticketValidType.list, val);
  if (val == 1) {
    return text;
  } else if (val == 5) {
    return data.validBeginDate + '~' + data.validEndDate;
  } else {
    return text.replace('X', data.validDayNum);
  }
};
// 门票
const columns1: TableColumnType[] = [
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 180,
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristName || '--';
    },
  },
  {
    title: '身份证',
    dataIndex: 'touristIdcard',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristIdcard || '--';
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '核销总数',
    dataIndex: 'verificationTotal',
    width: 180,
    align: 'center',
  },
  {
    title: '剩余可核销数',
    dataIndex: 'canVerificationNum',
    width: 180,
    align: 'center',
  },
  {
    title: '最后核销时间',
    dataIndex: 'lastVerificationTime',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];
// 套票
const columns2: TableColumnType[] = [
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 260,
  },
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    width: 180,
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '核销总数',
    dataIndex: 'verificationTotal',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '剩余可核销数',
    dataIndex: 'canVerificationNum',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.hasChild == 1) {
        return '';
      }
    },
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
  },
  {
    title: '最后核销时间',
    dataIndex: 'lastVerificationTime',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];
const columns3: TableColumnType[] = [
  {
    title: '卡号',
    dataIndex: 'cardNo',
    width: 180,
  },
  {
    title: '核销码',
    dataIndex: 'verificationCode',
    width: 180,
  },
  {
    title: '出行人',
    dataIndex: 'touristName',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristName || '--';
    },
  },
  {
    title: '身份证',
    dataIndex: 'touristIdcard',
    width: 180,
    align: 'center',
    customRender: ({ record }: any) => {
      return record.touristIdcard || '--';
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 180,
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderRefundStatus.list.find(
        (item: any) => item.value === record.refundStatus,
      )?.label;
    },
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
  },
];

// 卡
const cardColumns: TableColumnType[] = [
  {
    title: '门票名称',
    dataIndex: 'ticketName',
    align: 'center',
    width: '50%',
  },
  {
    title: '使用次数',
    dataIndex: 'limit',
    align: 'center',
    width: '50%',
  },
];
</script>
<template>
  <Page auto-content-height>
    <div class="bg-card">
      <PageHeader
        title="订单详情"
        class="p-3"
        @back="() => $router.back()"
      ></PageHeader>
      <div class="px-3 pb-3">
        <Card title="订单信息" class="mb-4">
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              orderInfo.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="下单时间">{{
              orderInfo.orderTime
            }}</DescriptionsItem>
            <DescriptionsItem label="订单状态">{{
              filterText(
                accessAllEnums?.ticketOrderStatus.list,
                orderInfo.orderStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付状态">{{
              filterText(
                accessAllEnums?.orderPayStatus.list,
                orderInfo.payStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="支付方式">{{
              filterText(
                accessAllEnums?.orderPayMethod.list,
                orderInfo.payMethod,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="订单来源">{{
              filterText(
                accessAllEnums?.ticketOrderSource.list,
                orderInfo.orderSource,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="联系人">
              {{ orderInfo.userInfo?.name }}
              <span class="ml-2" v-if="orderInfo.userInfo?.phone">{{
                orderInfo.userInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="订单金额">
              {{ orderInfo.orderPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="优惠金额">
              {{ orderInfo.discountPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="实付金额">
              {{ orderInfo.actualPrice }}
            </DescriptionsItem>
          </Descriptions>
        </Card>
        <template
          v-for="(item, index) in orderInfo.orderItemList"
          :key="item.id"
        >
          <Card class="mb-5">
            <div class="mb-2 flex justify-between">
              <h3 class="text-[16px] font-[600]">门票信息</h3>
            </div>
            <Descriptions>
              <DescriptionsItem label="门票名称">{{
                item.ticketName +
                '（' +
                filterText(accessAllEnums.ticketModel.list, item.model) +
                '）'
              }}</DescriptionsItem>
              <DescriptionsItem label="单价">{{
                item.unitPrice
              }}</DescriptionsItem>
              <DescriptionsItem label="数量">{{
                item.ticketNum
              }}</DescriptionsItem>
              <DescriptionsItem label="实名制">{{
                filterText(
                  accessAllEnums.tickeAuthenticationType.list,
                  item.authenticationType,
                )
              }}</DescriptionsItem>
              <DescriptionsItem label="退票规则">{{
                filterText(
                  accessAllEnums.ticketRefundType.list,
                  item.refundType,
                )
              }}</DescriptionsItem>
              <DescriptionsItem label="有效期">
                <!-- {{ filterValidText(item.validType, item) }} -->
                {{
                  item.validType == 1
                    ? item.validBeginDate + '当天有效'
                    : item.validBeginDate
                      ? item.validBeginDate + '~' + item.validEndDate
                      : ''
                }}
              </DescriptionsItem>
              <DescriptionsItem label="场次" v-if="item.periodId > 0">{{
                item.periodBeginTime + '~' + item.periodEndTime
              }}</DescriptionsItem>
              <DescriptionsItem label="二维码规则">
                {{
                  filterText(
                    accessAllEnums.tickeQrcodeRule.list,
                    item.qrcodeRule,
                  )
                }}
              </DescriptionsItem>
            </Descriptions>
            <Table
              :columns="cardColumns"
              :pagination="false"
              :data-source="item.childList"
              v-if="[3, 4, 5].includes(item.model)"
              class="mb-5"
              bordered
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex == 'limit'">
                  <p v-if="record.isLimit == 0">不限次数</p>
                  <p v-else>
                    共{{ record.totalLimit }}次，每月最多{{
                      record.monthLimit
                    }}次，每日最多{{ record.dayLimit }}次
                  </p>
                </template>
              </template>
            </Table>
            <div class="mb-2 text-[16px] font-[600]">订单明细</div>
            <!-- 门票订单明细表格 -->
            <Table
              :columns="columns1"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              v-if="item.model == 1"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex == 'lastVerificationTime'">
                  <p v-if="record.lastVerificationTime">
                    {{ record.lastVerificationTime }} <br />
                    <Button
                      type="link"
                      size="small"
                      @click="
                        showVerifyLog(
                          record.id,
                          record.orderId,
                          record.orderItemId,
                        )
                      "
                      >查看记录</Button
                    >
                  </p>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <Button
                    type="link"
                    size="small"
                    v-if="record.refundStatus == 0"
                    @click="handleVerify(record)"
                    >核销</Button
                  >
                </template>
              </template>
            </Table>
            <!-- 套票订单明细表格 -->
            <Table
              :columns="columns2"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              childrenColumnName="orderDetailChildList"
              :indentSize="10"
              defaultExpandAllRows
              v-if="item.model == 2"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'touristName'">
                  <div
                    v-if="record.hasChild == 0 && !record.orderDetailChildList"
                  >
                    <p>{{ record.touristName }}</p>
                    <p>{{ record.touristIdcard }}</p>
                  </div>
                  <div v-else>
                    <p>{{ record.hasChild == 1 ? '' : record.touristName }}</p>
                    <p>
                      {{ record.hasChild == 1 ? '' : record.touristIdcard }}
                    </p>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div v-if="record.hasChild == 1">
                    <Button
                      type="link"
                      size="small"
                      v-if="
                        [2, 3].includes(record.orderStatus) &&
                        [0, 2, 5].includes(record.refundStatus)
                      "
                      @click="handleVerify(record)"
                      >核销</Button
                    >
                    <Button
                      type="link"
                      size="small"
                      v-if="[5, 3].includes(record.orderStatus)"
                      @click="
                        showVerifyLog(
                          record.id,
                          record.orderId,
                          record.orderItemId,
                        )
                      "
                      >查看记录</Button
                    >
                  </div>
                </template>
              </template>
            </Table>
            <!-- 卡类订单明细表格 -->
            <Table
              :columns="columns3"
              :data-source="item.ticketOrderDetailList"
              :pagination="false"
              v-if="[3, 4, 5].includes(item.model)"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'action'">
                  <Button
                    type="link"
                    size="small"
                    v-if="record.refundStatus == 0"
                    @click="handleVerify(record)"
                    >核销</Button
                  >
                  <Button
                    type="link"
                    size="small"
                    @click="
                      showVerifyLog(
                        record.id,
                        record.orderId,
                        record.orderItemId,
                      )
                    "
                    >查看记录</Button
                  >
                </template>
              </template>
            </Table>
          </Card>
        </template>
      </div>
    </div>
    <VerifyLogModal></VerifyLogModal>
    <VerifyModal @success="getOrderInfoData"></VerifyModal>
  </Page>
</template>
